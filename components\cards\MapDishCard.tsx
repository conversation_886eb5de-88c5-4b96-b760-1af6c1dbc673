import React, { useState, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  X,
  Star,
  MapPin,
  Clock,
  Users,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Dish } from "@/types/host";

interface MapDishCardProps {
  dish: Dish;
  hostTitle: string;
  hostAddress: {
    street: string;
    city: string;
    state: string;
  };
  onClose: () => void;
  onViewDetails: () => void;
}

const MapDishCard: React.FC<MapDishCardProps> = ({
  dish,
  hostTitle,
  hostAddress,
  onClose,
  onViewDetails,
}) => {
  const router = useRouter();

  // Carousel state
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const images =
    dish.photos && dish.photos.length > 0 ? dish.photos : ["/placeholder.svg"];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(price);
  };

  // Carousel navigation functions
  const goToPrevious = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      setCurrentImageIndex((prev) =>
        prev === 0 ? images.length - 1 : prev - 1
      );
    },
    [images.length]
  );

  const goToNext = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      setCurrentImageIndex((prev) =>
        prev === images.length - 1 ? 0 : prev + 1
      );
    },
    [images.length]
  );

  const goToSlide = useCallback((index: number, e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex(index);
  }, []);

  // Touch/swipe support
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && images.length > 1) {
      setCurrentImageIndex((prev) =>
        prev === images.length - 1 ? 0 : prev + 1
      );
    }
    if (isRightSwipe && images.length > 1) {
      setCurrentImageIndex((prev) =>
        prev === 0 ? images.length - 1 : prev - 1
      );
    }
  }, [touchStart, touchEnd, images.length]);

  // Keyboard navigation support
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (images.length <= 1) return;

      if (e.key === "ArrowLeft") {
        e.preventDefault();
        setCurrentImageIndex((prev) =>
          prev === 0 ? images.length - 1 : prev - 1
        );
      } else if (e.key === "ArrowRight") {
        e.preventDefault();
        setCurrentImageIndex((prev) =>
          prev === images.length - 1 ? 0 : prev + 1
        );
      }
    },
    [images.length]
  );

  const getAvailabilityText = () => {
    if (!dish.availability || dish.availability.length === 0) {
      return "No availability";
    }

    const nextAvailable = dish.availability[0];
    const date = new Date(nextAvailable.date);
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();

    if (isToday) {
      return `Today ${nextAvailable.startTime} - ${nextAvailable.endTime}`;
    } else {
      return `${date.toLocaleDateString()} ${nextAvailable.startTime} - ${
        nextAvailable.endTime
      }`;
    }
  };

  return (
    <Card className="w-[280px] bg-white shadow-lg border-0 overflow-hidden">
      {/* Image Carousel */}
      <div
        className="relative h-36 w-full group focus:outline-none"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onKeyDown={handleKeyDown}
        tabIndex={images.length > 1 ? 0 : -1}
        role={images.length > 1 ? "region" : undefined}
        aria-label={images.length > 1 ? "Image carousel" : undefined}
      >
        {/* Close Button */}
        <div className="absolute top-2 right-2 z-20">
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 bg-white/80 hover:bg-white rounded-full shadow-sm"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Discount Badge */}
        {dish.discount && (
          <div className="absolute top-2 left-2 z-20 bg-primary text-white px-2 py-1 rounded text-xs font-medium">
            {dish.discount}% OFF
          </div>
        )}

        {/* Current Image */}
        <Image
          src={images[currentImageIndex]}
          alt={`${dish.name} - Image ${currentImageIndex + 1}`}
          fill
          className="object-cover transition-opacity duration-300"
          sizes="320px"
        />

        {/* Navigation Arrows - Only show if more than 1 image */}
        {images.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="sm"
              onClick={goToPrevious}
              className="absolute left-2 top-1/2 -translate-y-1/2 z-10 h-8 w-8 p-0 bg-white/80 hover:bg-white text-black rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={goToNext}
              className="absolute right-2 top-1/2 -translate-y-1/2 z-10 h-8 w-8 p-0 bg-white/80 hover:bg-white text-black rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </>
        )}

        {/* Dots Indicator - Only show if more than 1 image */}
        {images.length > 1 && (
          <div className="absolute bottom-2 left-1/2 -translate-x-1/2 z-10 flex gap-1">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={(e) => goToSlide(index, e)}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  index === currentImageIndex
                    ? "bg-green-500 scale-110"
                    : "bg-green-500/60 hover:bg-green-500/80"
                }`}
                aria-label={`Go to image ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      <CardContent className="p-3">
        {/* Dish Name and Rating */}
        <div className="flex items-start justify-between mb-1">
          <h3 className="font-semibold text-sm leading-tight pr-2 flex-1">
            {dish.name}
          </h3>
          <div className="flex items-center gap-1 shrink-0">
            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
            <span className="text-xs font-medium">
              {dish.averageRating?.toFixed(1) || "New"}
            </span>
          </div>
        </div>

        {/* Host and Location */}
        <div className="flex items-center gap-1 text-gray-600 mb-1">
          <MapPin className="h-3 w-3" />
          <span className="text-xs">
            {hostTitle} • {hostAddress.city}, {hostAddress.state}
          </span>
        </div>

        {/* Cuisine */}
        <div className="text-xs text-gray-600 mb-1">
          {typeof dish.cuisine === "object" ? dish.cuisine.name : dish.cuisine}
          {dish.subcuisine && ` • ${dish.subcuisine}`}
        </div>

        {/* Description */}
        <p
          className="text-xs text-gray-700 mb-2 overflow-hidden"
          style={{
            display: "-webkit-box",
            WebkitLineClamp: 1,
            WebkitBoxOrient: "vertical",
          }}
        >
          {dish.description}
        </p>

        {/* Availability */}
        <div className="flex items-center gap-1 text-gray-600 mb-2">
          <Clock className="h-3 w-3" />
          <span className="text-xs">{getAvailabilityText()}</span>
        </div>

        {/* Servings */}
        <div className="flex items-center gap-1 text-gray-600 mb-2">
          <Users className="h-3 w-3" />
          <span className="text-xs">
            {dish.minServings === dish.maxServings
              ? `${dish.minServings} serving${dish.minServings > 1 ? "s" : ""}`
              : `${dish.minServings}-${dish.maxServings} servings`}
          </span>
        </div>

        {/* Price and Action */}
        <div className="flex items-center justify-between">
          <div className="flex items-baseline gap-1">
            <span className="text-sm font-bold">
              {formatPrice(
                dish.discount
                  ? dish.price * (1 - dish.discount / 100)
                  : dish.price
              )}
            </span>
            <span className="text-sm text-gray-600">per serving</span>
          </div>
          <Button
            onClick={onViewDetails}
            size="sm"
            className="bg-primary hover:bg-green-600 text-white"
          >
            View Details
          </Button>
        </div>

        {/* Offering Types */}
        <div className="flex gap-1 mt-2">
          {dish.offering?.map((type, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              className="px-2 py-1 h-6 text-xs rounded-full border-gray-300 hover:bg-primary hover:text-white hover:border-primary"
              onClick={(e) => {
                e.stopPropagation();
                router.push(
                  `/dish/${dish._id}?offering=${encodeURIComponent(type)}`
                );
              }}
            >
              {type}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default MapDishCard;
