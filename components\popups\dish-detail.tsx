import type React from "react";
import { useState, useEffect } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import type { Dish, DiningLoc<PERSON>, <PERSON><PERSON>sine } from "@/types/host";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Image from "next/image";
import { editDish } from "@/api/host";
import FullPageAddDish from "@/components/addDish";
import { Calendar } from "@/components/ui/calendar";
import { TimePicker } from "@/components/ui/time-picker";
import { addDays, format, isSameDay, parseISO } from "date-fns";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { DateRange } from "react-day-picker";

interface DishDetailProps {
  dish: Dish;
  onClose: () => void;
  onUpdate: (updatedDish: Dish) => void;
  ingredients: { value: string; label: string }[];
  cuisines: { value: string; label: string; subcuisines: string[] }[];
  diningLocations: DiningLocations[];
}

interface AvailabilityTime {
  date: Date;
  startTime: string;
  endTime: string;
}

type DishFormData = {
  _id: string;
  name: string;
  price: number;
  discount?: number;
  description: string;
  diningLocation: string;
  offering: string[];
  photos: string[];
  ingredients: string[];
  cuisine: string;
  subcuisine?: string;
  minServings: number;
  maxServings: number;
  premade: boolean;
  currency: string;
  availability: {
    date: Date;
    startTime: string;
    endTime: string;
  }[];
};

const DishDetail: React.FC<DishDetailProps> = ({
  dish,
  onClose,
  onUpdate,
  ingredients,
  cuisines,
  diningLocations,
}) => {
  const [isPublished, setIsPublished] = useState(dish.published);
  const [isEditing, setIsEditing] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange>();
  const [availabilityTimes, setAvailabilityTimes] = useState<
    AvailabilityTime[]
  >([]);

  useEffect(() => {
    if (dateRange) {
      const start = dateRange.from;
      const end = dateRange.to;
      if (!start || !end) return;
      const newAvailabilityTimes: AvailabilityTime[] = [];
      for (let date = start; date <= end; date = addDays(date, 1)) {
        const existingTime = availabilityTimes.find((a) =>
          isSameDay(parseISO(a.date.toString()), date)
        );
        if (existingTime) {
          newAvailabilityTimes.push(existingTime);
        } else {
          newAvailabilityTimes.push({
            date: date,
            startTime: "09:00",
            endTime: "17:00",
          });
        }
      }
      setAvailabilityTimes(newAvailabilityTimes);
    }
  }, [dateRange]);

  const handlePublishToggle = async () => {
    try {
      if (!dish._id) return;
      const updatedDish = await editDish(dish._id, {
        published: !isPublished,
      });
      setIsPublished(!isPublished);
      onUpdate(updatedDish);
      toast.success(
        `Dish ${isPublished ? "unpublished" : "published"} successfully!`
      );
    } catch (error) {
      toast.error(
        `Failed to ${
          isPublished ? "unpublish" : "publish"
        } dish. Please try again.`
      );
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleEditComplete = (updatedDish: DishFormData) => {
    setIsEditing(false);
    // Convert DishFormData to Dish type
    const dishToUpdate: Dish = {
      ...dish,
      ...updatedDish,
      cuisine:
        typeof updatedDish.cuisine === "string"
          ? { _id: updatedDish.cuisine, name: "", code: "" }
          : (updatedDish.cuisine as unknown as Cuisine),
    };
    onUpdate(dishToUpdate);
    toast.success("Dish updated successfully!");
  };

  const handleTimeChange = (
    index: number,
    field: "startTime" | "endTime",
    value: string
  ) => {
    const newAvailabilityTimes = [...availabilityTimes];
    newAvailabilityTimes[index][field] = value;
    setAvailabilityTimes(newAvailabilityTimes);
  };

  if (isEditing) {
    return (
      <FullPageAddDish
        onAddDish={handleEditComplete}
        onClose={() => setIsEditing(false)}
        ingredients={ingredients}
        cuisines={cuisines}
        diningLocations={diningLocations}
        initialData={dish}
        premadeDishes={[]}
        isEditing={true}
      />
    );
  }

  return (
    <div className="fixed inset-0 bg-white z-50 overflow-auto p-6">
      <div className="max-w-3xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold">{dish.name}</h1>

        <Carousel className="w-full max-w-xs mx-auto">
          <CarouselContent>
            {dish.photos.map((photo, index) => (
              <CarouselItem key={index}>
                <Image
                  src={photo || "/placeholder.svg"}
                  alt={`${dish.name} - Image ${index + 1}`}
                  width={300}
                  height={200}
                  objectFit="cover"
                  className="rounded-lg"
                />
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>

        <div className="space-y-4">
          <p className="text-gray-600">{dish.description}</p>
          <p className="text-xl font-semibold">
            {dish.currency} {dish.price.toFixed(2)}
            {dish.discount && dish.discount > 0 && (
              <span className="ml-2 text-sm text-green-600">
                ({dish.discount}% off)
              </span>
            )}
          </p>
          <p>Cuisine: {dish.cuisine.name}</p>
          {dish.subcuisine && <p>Subcuisine: {dish.subcuisine}</p>}
          <p>
            Servings: {dish.minServings} - {dish.maxServings}
          </p>
          <div>
            <h3 className="font-semibold">Ingredients:</h3>
            <ul className="list-disc list-inside">
              {dish.ingredients.map((ingredient, index) => (
                <li key={index}>{ingredient}</li>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="font-semibold">Offering:</h3>
            <ul className="list-disc list-inside">
              {dish.offering.map((offer, index) => (
                <li key={index}>{offer}</li>
              ))}
            </ul>
          </div>
          <p>
            Dining Location:{" "}
            {typeof dish.diningLocation === "string"
              ? dish.diningLocation
              : (dish.diningLocation as any)?.locationName || "Unknown"}
          </p>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch
              checked={isPublished}
              onCheckedChange={handlePublishToggle}
              id="publish-toggle"
              className={cn(
                isPublished ? "bg-green-500" : "bg-gray-200",
                "relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              )}
            />
            <Label htmlFor="publish-toggle">
              {isPublished ? "Published" : "Unpublished"}
            </Label>
          </div>
          <Button onClick={handleEdit}>Edit Dish</Button>
        </div>

        <Button onClick={onClose} variant="outline" className="mt-6">
          Close
        </Button>
      </div>
    </div>
  );
};

export default DishDetail;
