/**
 * Date and Time Utility Functions
 * Handles timezone conversion and formatting for the dish filtering system
 */

/**
 * Converts a local date and time to GMT timezone
 * @param date - The selected date
 * @param time - Time in HH:MM format
 * @returns ISO string in GMT timezone
 */
export function convertToGMT(date: Date, time: string): string {
  const [hours, minutes] = time.split(':').map(Number);
  
  // Create a new date object with the selected date and time
  const localDateTime = new Date(date);
  localDateTime.setHours(hours, minutes, 0, 0);
  
  // Convert to GMT by getting the ISO string
  return localDateTime.toISOString();
}

/**
 * Formats a date for API consumption (YYYY-MM-DD format)
 * @param date - The date to format
 * @returns Date string in YYYY-MM-DD format
 */
export function formatDateForAPI(date: Date): string {
  return date.toISOString().split('T')[0];
}

/**
 * Formats time for API consumption (HH:MM format)
 * @param time - Time string in various formats
 * @returns Time string in HH:MM format
 */
export function formatTimeForAPI(time: string): string {
  // Ensure time is in HH:MM format
  const [hours, minutes] = time.split(':');
  return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}`;
}

/**
 * Validates if end time is after start time
 * @param startTime - Start time in HH:MM format
 * @param endTime - End time in HH:MM format
 * @returns boolean indicating if the time range is valid
 */
export function isValidTimeRange(startTime: string, endTime: string): boolean {
  const [startHours, startMinutes] = startTime.split(':').map(Number);
  const [endHours, endMinutes] = endTime.split(':').map(Number);
  
  const startTotalMinutes = startHours * 60 + startMinutes;
  const endTotalMinutes = endHours * 60 + endMinutes;
  
  return endTotalMinutes > startTotalMinutes;
}

/**
 * Generates time options for time picker (30-minute intervals)
 * @returns Array of time strings in HH:MM format
 */
export function generateTimeOptions(): string[] {
  const options = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const time = `${hour.toString().padStart(2, "0")}:${minute
        .toString()
        .padStart(2, "0")}`;
      options.push(time);
    }
  }
  return options;
}

/**
 * Converts GMT time back to local time for display
 * @param gmtISOString - GMT time in ISO string format
 * @returns Local time in HH:MM format
 */
export function convertFromGMTToLocal(gmtISOString: string): string {
  const date = new Date(gmtISOString);
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
}

/**
 * Gets the current date in the user's timezone
 * @returns Date object for today
 */
export function getCurrentDate(): Date {
  return new Date();
}

/**
 * Checks if a date is in the past
 * @param date - Date to check
 * @returns boolean indicating if the date is in the past
 */
export function isPastDate(date: Date): boolean {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const checkDate = new Date(date);
  checkDate.setHours(0, 0, 0, 0);
  return checkDate < today;
}
