"use client";

import type React from "react";
import { useEffect, useState, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Heart,
  UtensilsCrossed,
  ChevronLeft,
  ChevronRight,
  HomeIcon,
  Clock,
} from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import useEmblaCarousel from "embla-carousel-react";
import { useRouter } from "next/navigation";

interface DishCardProps {
  _id: string;
  name: string;
  price: number;
  discount?: number;
  description: string;
  diningLocation: {
    _id: string;
    locationName: string;
    images: string[];
  };
  offering: string[];
  averageRating: number;
  photos: string[];
  ingredients: string[];
  cuisine: {
    _id: string;
    name: string;
  };
  subcuisine?: string;
  minServings: number;
  maxServings: number;
  premade: boolean;
  currency: string;
  availability?: Array<{
    startTimeGMT: string | null;
    endTimeGMT: string | null;
    date: string;
    startTime: string;
    endTime: string;
    _id: string;
  }>;
  onClick?: () => void;
}

const HomeDishCard: React.FC<DishCardProps> = ({
  _id,
  name,
  price,
  discount,
  description,
  diningLocation,
  offering,
  averageRating,
  photos,
  ingredients,
  cuisine,
  subcuisine,
  minServings,
  maxServings,
  currency,
  availability,
  onClick,
}) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [isHearted, setIsHearted] = useState(false);
  const [showDiningImages, setShowDiningImages] = useState(false);

  const router = useRouter();

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setCurrentIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
  }, [emblaApi, onSelect]);

  const scrollPrev = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      if (emblaApi) emblaApi.scrollPrev();
    },
    [emblaApi]
  );

  const scrollNext = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      if (emblaApi) emblaApi.scrollNext();
    },
    [emblaApi]
  );

  const scrollTo = useCallback(
    (index: number) => (e: React.MouseEvent) => {
      e.stopPropagation();
      if (emblaApi) emblaApi.scrollTo(index);
    },
    [emblaApi]
  );

  const toggleHeart = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsHearted(!isHearted);
  };

  const toggleImages = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      setShowDiningImages(!showDiningImages);
    },
    [showDiningImages]
  );

  const discountedPrice = discount ? price - (price * discount) / 100 : price;

  const getTodayAvailability = useCallback(() => {
    if (!availability || availability.length === 0) return null;

    const today = new Date();
    // Get the user's local date part
    const todayLocal = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );

    return availability.filter((slot) => {
      // Use startTimeGMT to determine the date in the user's local timezone
      if (!slot.startTimeGMT) return false; // Cannot determine availability without GMT time

      try {
        const slotStartUTC = new Date(slot.startTimeGMT);
        // Convert the UTC start time to the user's local timezone and get the date part
        const slotStartDateLocal = new Date(
          slotStartUTC.getFullYear(),
          slotStartUTC.getMonth(),
          slotStartUTC.getDate()
        );

        // Compare the local date parts
        return slotStartDateLocal.getTime() === todayLocal.getTime();
      } catch (error) {
        console.error("Error processing availability slot date:", slot, error);
        return false;
      }
    });
  }, [availability]);

  const todaySlots = getTodayAvailability();

  return (
    <Card
      className={cn(
        "w-full bg-white border-none cursor-pointer overflow-hidden transition-all duration-300 ease-out",
        isHovered
          ? "shadow-lg z-10 transform -translate-y-[5px]"
          : "shadow-sm hover:shadow-md"
      )}
      onClick={(e) => {
        // Prevent navigation if clicking on buttons
        if ((e.target as HTMLElement).closest("button")) {
          return;
        }
        router.push(`/dish/${_id}`);
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative">
        {/* Image Carousel */}
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex">
            {(showDiningImages ? diningLocation?.images : photos || []).map(
              (photo, index) => (
                <div className="flex-[0_0_100%]" key={index}>
                  <div className="relative aspect-square w-full overflow-hidden">
                    <Image
                      src={photo || "/placeholder.svg"}
                      alt={`${
                        showDiningImages ? "Dining Location" : name
                      } - Photo ${index + 1}`}
                      fill
                      className="object-cover"
                      sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                      priority={index === 0}
                    />
                  </div>
                </div>
              )
            )}
          </div>
        </div>

        {/* Navigation Controls */}
        <div
          className={cn(
            "absolute inset-0 transition-opacity duration-300",
            isHovered ? "opacity-100" : "opacity-0"
          )}
        >
          <button
            className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/70 hover:bg-white rounded-full p-1.5 shadow-sm transition-all duration-300 ease-out"
            onClick={scrollPrev}
          >
            <ChevronLeft
              className={cn(
                "w-4 h-4 transition-transform duration-300 ease-out",
                isHovered ? "transform scale-110" : ""
              )}
            />
          </button>
          <button
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/70 hover:bg-white rounded-full p-1.5 shadow-sm transition-all duration-300 ease-out"
            onClick={scrollNext}
          >
            <ChevronRight
              className={cn(
                "w-4 h-4 transition-transform duration-300 ease-out",
                isHovered ? "transform scale-110" : ""
              )}
            />
          </button>
        </div>

        {/* Toggle Images Button */}
        <button
          onClick={toggleImages}
          className={`absolute left-3 top-3 z-10 rounded-full p-2 shadow-sm transition-all duration-300 ease-out ${
            showDiningImages
              ? "bg-green-500 text-white hover:bg-green-600"
              : "bg-white/70 text-black hover:bg-white"
          }`}
          aria-label={
            showDiningImages
              ? "Show dish images"
              : "Show dining location images"
          }
        >
          <HomeIcon
            className={cn(
              "h-4 w-4 transition-transform duration-300 ease-out",
              isHovered ? "transform scale-110" : ""
            )}
          />
        </button>

        {/* Heart Button */}
        <button
          onClick={toggleHeart}
          className="absolute right-3 top-3 z-10 rounded-full p-2 shadow-sm bg-white/70 hover:bg-white transition-all duration-300 ease-out"
          aria-label={isHearted ? "Remove from favorites" : "Add to favorites"}
        >
          <Heart
            className={cn(
              "h-5 w-5 transition-transform duration-300 ease-out",
              isHovered ? "transform scale-110" : "",
              isHearted
                ? "fill-green-500 stroke-green-500"
                : "fill-none stroke-gray-700"
            )}
          />
        </button>

        {/* Carousel Indicators */}
        <div className="absolute bottom-2 left-0 right-0 flex justify-center gap-1">
          {photos.map((_, index) => (
            <button
              key={index}
              onClick={scrollTo(index)}
              className={cn(
                "h-1.5 rounded-full bg-green-500 transition-all duration-300 ease-out",
                currentIndex === index ? "w-4" : "w-1.5",
                isHovered ? "transform scale-110" : ""
              )}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>

      {/* Card Content */}
      <CardContent className="p-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between flex-wrap gap-1">
            <h3 className="text-base font-medium truncate max-w-[70%]">
              {name}
            </h3>
            <div className="flex items-center gap-1 shrink-0">
              <svg viewBox="0 0 32 32" className="h-3 w-3 fill-yellow-500">
                <path d="M15.094 1.579l-4.124 9.997-10.742 0.859c-0.799 0.064-1.119 1.074-0.517 1.609l8.072 7.153-2.442 10.534c-0.184 0.798 0.678 1.436 1.379 1.005l9.279-5.555 9.279 5.555c0.701 0.431 1.563-0.207 1.379-1.005l-2.442-10.534 8.072-7.153c0.602-0.534 0.282-1.545-0.517-1.609l-10.742-0.859-4.124-9.997c-0.317-0.768-1.43-0.768-1.747 0z" />
              </svg>
              <span className="text-sm font-medium">
                {averageRating.toFixed(2)}
              </span>
            </div>
          </div>
          <p className="text-sm text-gray-500 truncate">
            {diningLocation.locationName}
          </p>
          <p className="text-sm">
            <span className="font-semibold">
              {currency} {discountedPrice.toFixed(2)}
            </span>
            {discount && (
              <span className="ml-2 text-xs line-through text-gray-500">
                {currency} {price.toFixed(2)}
              </span>
            )}
          </p>
        </div>

        {/* Hover Details - Only visible when hovered */}
        <div
          className={cn(
            "mt-2 pt-2 border-t border-gray-100 transition-all duration-300 ease-in-out overflow-hidden",
            isHovered ? "max-h-[300px] opacity-100" : "max-h-0 opacity-0"
          )}
        >
          <div className="grid grid-cols-2 gap-x-2 gap-y-2 text-xs">
            <div>
              <h4 className="font-semibold flex items-center gap-1 mb-1">
                <UtensilsCrossed className="h-3 w-3" />
                Cuisine
              </h4>
              <div className="flex flex-wrap gap-1">
                <span className="bg-gray-100 px-1.5 py-0.5 rounded-full text-xs">
                  {cuisine.name}
                </span>
                {subcuisine && (
                  <span className="bg-gray-100 px-1.5 py-0.5 rounded-full text-xs">
                    {subcuisine}
                  </span>
                )}
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-1">Servings</h4>
              <p className="text-gray-600">
                {minServings === maxServings
                  ? `${minServings} ${minServings === 1 ? "person" : "people"}`
                  : `${minServings}-${maxServings} people`}
              </p>
            </div>

            <div className="col-span-2">
              <h4 className="font-semibold mb-1">Ingredients</h4>
              <p className="text-gray-600 line-clamp-2">
                {ingredients.join(", ")}
              </p>
            </div>

            <div className="col-span-2">
              <h4 className="font-semibold mb-1">Offering</h4>
              <div className="flex flex-wrap gap-1">
                {offering.map((offer) => (
                  <span
                    key={offer}
                    className="bg-gray-100 px-1.5 py-0.5 rounded-full text-xs"
                  >
                    {offer}
                  </span>
                ))}
              </div>
            </div>

            {todaySlots && todaySlots.length > 0 && (
              <div className="col-span-2">
                <h4 className="font-semibold flex items-center gap-1 mb-1">
                  <Clock className="h-3 w-3" />
                  Available Today
                </h4>
                <div className="flex flex-wrap gap-1">
                  {todaySlots.map((slot, index) => (
                    <span
                      key={index}
                      className="bg-green-100 text-green-800 px-1.5 py-0.5 rounded-full text-xs"
                    >
                      {slot.startTimeGMT
                        ? new Date(slot.startTimeGMT).toLocaleTimeString([], {
                            // Use startTimeGMT directly
                            hour: "2-digit",
                            minute: "2-digit",
                          })
                        : slot.startTime}{" "}
                      -{" "}
                      {slot.endTimeGMT
                        ? new Date(slot.endTimeGMT).toLocaleTimeString([], {
                            // Use endTimeGMT directly
                            hour: "2-digit",
                            minute: "2-digit",
                          })
                        : slot.endTime}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default HomeDishCard;
