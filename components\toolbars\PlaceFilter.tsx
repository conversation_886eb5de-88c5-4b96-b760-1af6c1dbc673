// src/components/filters/PlaceFilter.tsx
import React, { useState, useRef, useCallback, useEffect } from "react";
import { Autocomplete } from "@react-google-maps/api";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { LocateFixed, Search, AlertCircle, CheckCircle } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface PlaceFilterProps {
  initialValue?: string;
  onPlaceSelect: (
    address: string,
    lat?: number,
    lng?: number,
    bounds?: google.maps.LatLngBounds
  ) => void;
  onSearchTrigger?: (query: string) => void; // New prop for manual search trigger
  isMapApiLoaded: boolean;
  googleMapsApiKey: string;
  libraries: ("places" | "drawing" | "geometry" | "visualization")[];
  placeholder?: string;
  allowCustomInput?: boolean; // Whether to allow searches with custom text
  disabled?: boolean;
  className?: string;
}

// Validation states for input
type ValidationState = "idle" | "validating" | "valid" | "invalid" | "empty";

const PlaceFilter: React.FC<PlaceFilterProps> = ({
  initialValue = "",
  onPlaceSelect,
  onSearchTrigger,
  isMapApiLoaded,
  googleMapsApiKey,
  libraries,
  placeholder = "Search places...",
  allowCustomInput = true,
  disabled = false,
  className,
}) => {
  const [inputValue, setInputValue] = useState(initialValue);
  const [validationState, setValidationState] =
    useState<ValidationState>("idle");
  const [lastValidPlace, setLastValidPlace] =
    useState<google.maps.places.PlaceResult | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();

  // Update internal state if initialValue changes externally
  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  // Cleanup debounce timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const onLoad = useCallback(
    (autocomplete: google.maps.places.Autocomplete) => {
      autocompleteRef.current = autocomplete;
      // console.log('Autocomplete loaded:', autocomplete);
    },
    []
  );

  const onPlaceChanged = useCallback(() => {
    if (autocompleteRef.current) {
      const place = autocompleteRef.current.getPlace();
      console.log("Place selected:", place);

      // Check if place is properly defined and has required properties
      if (place && place.formatted_address) {
        const lat = place.geometry?.location?.lat();
        const lng = place.geometry?.location?.lng();
        const bounds = place.geometry?.viewport;
        console.log("Place has formatted address and coordinates:", {
          address: place.formatted_address,
          lat,
          lng,
          bounds: bounds
            ? {
                north: bounds.getNorthEast().lat(),
                south: bounds.getSouthWest().lat(),
                east: bounds.getNorthEast().lng(),
                west: bounds.getSouthWest().lng(),
              }
            : null,
        });
        setInputValue(place.formatted_address);
        setLastValidPlace(place);
        setValidationState("valid");
        onPlaceSelect(place.formatted_address, lat, lng, bounds);
        toast.info("Location selected. Click search to find dishes.");
      } else if (place && place.name && inputRef.current?.value) {
        // Handle cases where formatted_address might be missing but name exists
        // Or use the raw input value if place object is incomplete
        const addressGuess = place.name || inputRef.current.value;
        console.log("Using place name as fallback:", addressGuess);
        setInputValue(addressGuess);
        toast.warning(
          "Could not get precise location, using name/input. Click search to find dishes."
        );
        onPlaceSelect(addressGuess); // No lat/lng guaranteed
      } else if (inputRef.current?.value) {
        // Fallback to raw input if API fails completely
        console.log("Using raw input as fallback:", inputRef.current.value);
        setInputValue(inputRef.current.value);
        toast.info("Location saved. Click search to find dishes.");
        onPlaceSelect(inputRef.current.value);
      }
    } else {
      console.error("Autocomplete ref not set");
      if (inputRef.current?.value) {
        // Allow searching even if autocomplete failed to load
        console.log(
          "Autocomplete not ready, using raw input:",
          inputRef.current.value
        );
        setInputValue(inputRef.current.value);
        toast.info("Location saved. Click search to find dishes.");
        onPlaceSelect(inputRef.current.value);
      }
    }
  }, [onPlaceSelect]);

  const validateInput = useCallback((value: string) => {
    if (!value.trim()) {
      setValidationState("empty");
      return false;
    }

    if (value.length < 2) {
      setValidationState("invalid");
      return false;
    }

    setValidationState("valid");
    return true;
  }, []);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);

    // Clear previous debounce
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set validation state to validating
    if (newValue.trim()) {
      setValidationState("validating");
    } else {
      setValidationState("empty");
    }

    // Debounce validation
    debounceTimeoutRef.current = setTimeout(() => {
      validateInput(newValue);
    }, 300);
  };

  const performSearch = useCallback(
    async (query: string) => {
      if (!query.trim()) {
        toast.error("Please enter a location to search");
        setValidationState("empty");
        return;
      }

      setIsSearching(true);
      setValidationState("validating");

      try {
        // First, try to use the last valid place if the input matches
        if (lastValidPlace && lastValidPlace.formatted_address === query) {
          console.log("Using cached place result:", lastValidPlace);
          const lat = lastValidPlace.geometry?.location?.lat();
          const lng = lastValidPlace.geometry?.location?.lng();
          const bounds = lastValidPlace.geometry?.viewport;

          onPlaceSelect(query, lat, lng, bounds);
          setValidationState("valid");
          toast.success("Location found successfully");
          return;
        }

        // If autocomplete is available and we have a place, use it
        if (autocompleteRef.current) {
          const place = autocompleteRef.current.getPlace();
          if (place && place.formatted_address) {
            console.log("Using autocomplete place:", place);
            const lat = place.geometry?.location?.lat();
            const lng = place.geometry?.location?.lng();
            const bounds = place.geometry?.viewport;

            onPlaceSelect(place.formatted_address, lat, lng, bounds);
            setLastValidPlace(place);
            setValidationState("valid");
            toast.success("Location found successfully");
            return;
          }
        }

        // If custom input is allowed, try geocoding
        if (allowCustomInput && isMapApiLoaded && window.google) {
          console.log("Attempting to geocode custom input:", query);
          const geocoder = new window.google.maps.Geocoder();
          const response = await geocoder.geocode({ address: query });

          if (response.results && response.results.length > 0) {
            const result = response.results[0];
            const lat = result.geometry.location.lat();
            const lng = result.geometry.location.lng();
            const bounds = result.geometry.viewport;

            console.log("Geocoding successful:", result);
            onPlaceSelect(result.formatted_address, lat, lng, bounds);
            setInputValue(result.formatted_address);
            setValidationState("valid");
            toast.success("Location found successfully");
            return;
          }
        }

        // If we have a search trigger callback and custom input is allowed
        if (onSearchTrigger && allowCustomInput) {
          console.log("Triggering custom search for:", query);
          onSearchTrigger(query);
          setValidationState("valid");
          toast.info(`Searching for "${query}"`);
          return;
        }

        // If we get here, the search failed
        setValidationState("invalid");
        if (allowCustomInput) {
          toast.error(
            "Location not found. Please try a different search term."
          );
        } else {
          toast.error("Please select a location from the suggestions.");
        }
      } catch (error) {
        console.error("Search error:", error);
        setValidationState("invalid");
        toast.error("An error occurred while searching. Please try again.");
      } finally {
        setIsSearching(false);
      }
    },
    [
      allowCustomInput,
      isMapApiLoaded,
      lastValidPlace,
      onPlaceSelect,
      onSearchTrigger,
    ]
  );

  // Enhanced keyboard handling with accessibility support
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    switch (event.key) {
      case "Enter":
        event.preventDefault();
        performSearch(inputValue);
        break;
      case "Escape":
        event.preventDefault();
        if (inputValue) {
          setInputValue("");
          setValidationState("empty");
          inputRef.current?.focus();
        }
        break;
      case "ArrowDown":
        // Let autocomplete handle arrow navigation
        break;
      case "ArrowUp":
        // Let autocomplete handle arrow navigation
        break;
      default:
        break;
    }
  };

  // Handle blur event for validation
  const handleBlur = useCallback(() => {
    if (inputValue.trim() && validationState === "validating") {
      validateInput(inputValue);
    }
  }, [inputValue, validationState, validateInput]);

  // Handle focus event
  const handleFocus = useCallback(() => {
    if (validationState === "invalid" || validationState === "empty") {
      setValidationState("idle");
    }
  }, [validationState]);

  const handleNearMe = useCallback(() => {
    if (!navigator.geolocation) {
      toast.error("Geolocation is not supported by your browser.");
      return;
    }

    console.log("Near Me clicked, libraries available:", libraries);

    const success = async (position: GeolocationPosition) => {
      const lat = position.coords.latitude;
      const lng = position.coords.longitude;
      // console.log(`Near Me Coords: ${lat}, ${lng}`);

      // Use Geocoding API to get address from lat/lng
      try {
        // Check if Geocoding API is available (requires 'geometry' library potentially, but often works)
        if (!window.google || !window.google.maps.Geocoder) {
          console.warn(
            "Google Geocoder not ready, using coordinates directly."
          );
          // Fallback: Use lat/lng directly if geocoder isn't ready (less user-friendly)
          const coordString = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
          setInputValue(`Near ${coordString}`);
          onPlaceSelect(coordString, lat, lng); // Send coords as string too
          toast.info("Current location found. Click search to find dishes.");
          return;
        }

        const geocoder = new window.google.maps.Geocoder();
        const latlng = { lat: lat, lng: lng };
        const response = await geocoder.geocode({ location: latlng });
        console.log("Geocoding Response:", response);
        if (response.results && response.results[0]) {
          const address = response.results[0].formatted_address;
          // console.log("Reverse Geocoded Address:", address);
          setInputValue(address);
          // For reverse geocoding, we don't have bounds info, so we pass undefined
          onPlaceSelect(address, lat, lng);
          toast.info("Current location found. Click search to find dishes.");
        } else {
          toast.error("Could not find address for your location.");
          const coordString = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
          setInputValue(`Near ${coordString}`);
          onPlaceSelect(coordString, lat, lng);
          toast.info(
            "Location coordinates saved. Click search to find dishes."
          );
        }
      } catch (error) {
        console.error("Error during reverse geocoding:", error);
        toast.error("Error finding address for your location.");
        const coordString = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        setInputValue(`Near ${coordString}`);
        onPlaceSelect(coordString, lat, lng);
        toast.info("Location coordinates saved. Click search to find dishes.");
      }
    };

    const error = (err: GeolocationPositionError) => {
      console.warn(`Geolocation ERROR(${err.code}): ${err.message}`);
      toast.error(`Could not get location: ${err.message}`);
    };

    navigator.geolocation.getCurrentPosition(success, error, {
      enableHighAccuracy: false, // Faster, less accurate
      timeout: 5000,
      maximumAge: 0,
    });
  }, [onPlaceSelect, googleMapsApiKey, libraries]); // Include libraries dependency

  // Get validation icon and styling
  const getValidationIcon = () => {
    switch (validationState) {
      case "validating":
        return (
          <div className="h-3 w-3 border border-gray-400 border-t-transparent rounded-full animate-spin" />
        );
      case "valid":
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case "invalid":
        return <AlertCircle className="h-3 w-3 text-red-500" />;
      default:
        return null;
    }
  };

  const getInputClassName = () => {
    const baseClasses = "pr-20 text-sm h-8 transition-colors duration-200";
    const validationClasses = {
      idle: "",
      validating: "border-blue-300 focus:border-blue-500",
      valid: "border-green-300 focus:border-green-500",
      invalid: "border-red-300 focus:border-red-500",
      empty: "border-gray-300",
    };

    return cn(
      baseClasses,
      validationClasses[validationState],
      className,
      disabled && "opacity-50 cursor-not-allowed"
    );
  };

  const getAriaDescribedBy = () => {
    const ids = [];
    if (validationState === "invalid") ids.push("search-error");
    if (validationState === "empty") ids.push("search-empty");
    return ids.length > 0 ? ids.join(" ") : undefined;
  };

  return (
    <div className="relative flex items-center w-full">
      {isMapApiLoaded ? (
        <Autocomplete
          onLoad={onLoad}
          onPlaceChanged={onPlaceChanged}
          options={{
            types: ["(regions)"],
            // componentRestrictions: { country: 'us' },
          }}
        >
          <Input
            ref={inputRef}
            type="text"
            placeholder={placeholder}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            onFocus={handleFocus}
            className={getInputClassName()}
            disabled={disabled}
            aria-label="Search for locations"
            aria-describedby={getAriaDescribedBy()}
            aria-invalid={validationState === "invalid"}
            aria-expanded={false}
            role="combobox"
            autoComplete="off"
          />
        </Autocomplete>
      ) : (
        <Input
          ref={inputRef}
          type="text"
          placeholder="Loading maps..."
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          onFocus={handleFocus}
          className={getInputClassName()}
          disabled={!googleMapsApiKey || disabled}
          aria-label="Search for locations"
          aria-describedby={getAriaDescribedBy()}
          aria-invalid={validationState === "invalid"}
          autoComplete="off"
        />
      )}

      {/* Validation indicator */}
      <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
        {getValidationIcon()}
      </div>

      {/* Near me button */}
      <Button
        type="button"
        variant="ghost"
        size="icon"
        className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
        onClick={handleNearMe}
        disabled={!isMapApiLoaded || disabled}
        title="Use my current location"
        aria-label="Use my current location"
      >
        <LocateFixed className="h-3 w-3" />
      </Button>

      {/* Screen reader announcements */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {validationState === "validating" && "Searching for location..."}
        {validationState === "valid" && "Location found"}
        {validationState === "invalid" && "Invalid location. Please try again."}
        {validationState === "empty" && "Please enter a location"}
      </div>

      {/* Error messages for accessibility */}
      {validationState === "invalid" && (
        <div id="search-error" className="sr-only">
          {allowCustomInput
            ? "Location not found. Please try a different search term."
            : "Please select a location from the suggestions."}
        </div>
      )}
      {validationState === "empty" && (
        <div id="search-empty" className="sr-only">
          Please enter a location to search
        </div>
      )}
    </div>
  );
};

export default PlaceFilter;
