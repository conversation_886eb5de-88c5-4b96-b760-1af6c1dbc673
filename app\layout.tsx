import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/context/AuthProvider";
import { ReactNode } from "react";
import { Toaster } from "sonner";
import { CartProvider } from "@/context/CartProvider";
import { CurrencyProvider } from "@/context/CurrencyProvider";
import { Providers } from "@/context/StripeProvider";
import { SocketProvider } from "@/context/SocketContext";
import NotificationStoreInitializer from "@/components/NotificationStoreInitializer";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "FamFoody",
  icons: "/famfoody-logo.png",
  description:
    "Discover the taste of home. FamFoody brings you authentic meals made by talented home cooks in your community. Order beloved family recipes or join a dinner table to share a meal.",
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <body>
        <Toaster position="bottom-left" richColors duration={2} />
        <AuthProvider>
          <SocketProvider>
            <NotificationStoreInitializer />
            <CartProvider>
              <Providers>
                <CurrencyProvider>{children}</CurrencyProvider>
              </Providers>
            </CartProvider>
          </SocketProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
