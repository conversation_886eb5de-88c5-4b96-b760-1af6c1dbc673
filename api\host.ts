import { Address } from "@/types/address";
import axiosInstance, { endpoints } from "@/utils/axiosInstance";

// get All hosts
export const getAllHosts = async (queryParams: any) => {
  try {
    const response = await axiosInstance.get(
      `${endpoints.host.allHost}?${queryParams}`
    );
    return response.data;
  } catch (error) {
    console.error("Error getting All Hosts for Home page:", error);
    throw error;
  }
};

// get All published dishes with search parameters
export const getAllPublishedDishes = async (queryParams: any) => {
  try {
    // Always include published=true in the query
    const params = new URLSearchParams(queryParams);
    params.set("published", "true");
    params.set("premade", "false");

    const response = await axiosInstance.get(
      `${endpoints.dish.all}?${params.toString()}`
    );
    const data = response.data.data;
    // Filter out DineIn offering from dishes if dineInAvailable is false and if no offering remains remove the dish
    const filteredData = data.filter((dish: any) => {
      if (dish.isAvailableInFuture) {
        console.log(queryParams);
        dish.offering = dish.offering.filter((offer: any) => {
          if (offer === "Delivery") {
            return dish.dishAvailable;
          }
          return true;
        });
        if (dish.offering.length === 0) return false;

        const offeringParam = params.get("offering");
        console.log("offeringParam", offeringParam);
        if (
          offeringParam?.split(",")?.includes("Delivery") &&
          !dish.offering.includes("Delivery")
        ) {
          return false;
        }
        return true;
      }
      return false;
    });
    return filteredData;
  } catch (error) {
    console.error("Error getting published dishes:", error);
    throw error;
  }
};

// Create Host
export const createHost = async (user: string) => {
  try {
    const response = await axiosInstance.post(endpoints.host.create, { user });
    return response.data;
  } catch (error) {
    console.error("Error creating host:", error);
    throw error;
  }
};

// Edit Host
export const editHost = async (id: number, data: any) => {
  try {
    const response = await axiosInstance.put(endpoints.host.edit(id), data);
    return response.data;
  } catch (error) {
    console.error("Error editing host:", error);
    throw error;
  }
};

// get Host profile
export const getHostProfile = async () => {
  try {
    const response = await axiosInstance.get(endpoints.host.details());
    console.log("🚀 ~ getHostProfile ~ response", response);
    return response.data;
  } catch (error) {
    console.error("Error getting host:", error);
    throw error;
  }
};

// get Host details with hostId
export const getHostDetails = async (hostId: string | undefined) => {
  try {
    const response = await axiosInstance.get(endpoints.host.hostInfo(hostId));
    return response.data;
  } catch (error) {
    console.error("Error getting host details:", error);
    throw error;
  }
};

// Add location to Host
export const locationHost = async (id: string, data: Address) => {
  try {
    const response = await axiosInstance.post(
      endpoints.host.location(id),
      data
    );
    return response.data;
  } catch (error) {
    console.error("Error creating location of host:", error);
    throw error;
  }
};

// Add Dining locations to Host
export const dininglocationHost = async (data: {
  locationName: string;
  images: string[];
}) => {
  try {
    console.log(
      "🚀 ~ file: host.ts ~ line 47 ~ dininglocationHost ~ data",
      data
    );
    const response = await axiosInstance.post(endpoints.host.dining(), data);
    console.log(
      "🚀 ~ file: host.ts ~ line 49 ~ dininglocationHost ~ response",
      response
    );
    return response.data;
  } catch (error) {
    console.error("Error creating dining location:", error);
    throw error;
  }
};

//Update Dining locations to Host
export const updateDininglocationHost = async (id: string, data: any) => {
  try {
    const response = await axiosInstance.put(endpoints.host.location(id), data);
    return response.data;
  } catch (error) {
    console.error("Error updating dining location:", error);
    throw error;
  }
};

// remove Dining locations to Host
export const removeDininglocationHost = async (
  hostId: string,
  diningId: string
) => {
  try {
    const response = await axiosInstance.delete(
      endpoints.host.removeDining(hostId, diningId)
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting dining location:", error);
    throw error;
  }
};

// get All Cuisines
export const getAllCuisines = async () => {
  try {
    const response = await axiosInstance.get(endpoints.cuisines.all);
    return response.data;
  } catch (error) {
    console.error("Error getting All Cuisines:", error);
    throw error;
  }
};

// get All Ingerdients
export const getAllIngredients = async () => {
  try {
    const response = await axiosInstance.get(endpoints.ingredients.all);
    return response.data;
  } catch (error) {
    console.error("Error getting All Ingredients:", error);
    throw error;
  }
};

// Get Premade Dishes
export const getPremadeDishes = async () => {
  try {
    const response = await axiosInstance.get(endpoints.dish.premade_dishes);
    return response.data;
  } catch (error) {
    console.error("Error getting premade dishes:", error);
    throw error;
  }
};

export const getAllDishes = async () => {
  try {
    const response = await axiosInstance.get(endpoints.dish.published_dishes);
    return response.data;
  } catch (error) {
    console.error("Error getting all dishes:", error);
    throw error;
  }
};

export const editDish = async (id: string, data: DishData) => {
  try {
    const response = await axiosInstance.put(endpoints.dish.edit(id), data);
    return response.data;
  } catch (error) {
    console.error("Error editing dish:", error);
    throw error;
  }
};

// Create Dish of host
interface AvailabilityTime {
  date: string; // Assuming date is sent as ISO string
  startTime: string;
  endTime: string;
  startTimeGMT?: string;
  endTimeGMT?: string;
}

interface DishData {
  name: string;
  price: number;
  discount?: number;
  description: string;
  diningLocation: string;
  subcuisine?: string;
  ingredients: string[];
  cuisine: string;
  photos: string[];
  minServings: number;
  maxServings: number;
  availability?: AvailabilityTime[]; // Add availability field
}

// Create Dish of host
export const createDishHost = async (dataDish: DishData) => {
  try {
    const response = await axiosInstance.post(endpoints.host.dish, dataDish);
    return response.data;
  } catch (error) {
    console.error("Error creating host dish:", error);
    throw error;
  }
};

// get Dish details
export const getDishDetails = async (id: string) => {
  try {
    const response = await axiosInstance.get(endpoints.dish.details(id));
    const data = response.data;
    const dish = data.data;
    dish.offering = dish.offering.filter((offer: any) => {
      if (offer === "Delivery") {
        return dish.dishAvailable;
      }
      return true;
    });
    return {
      data: dish,
      host: data.host,
    };
  } catch (error) {
    console.error("Error getting dish details:", error);
    throw error;
  }
};

// remove Dish From Host
export const removeDishFromHost = async (dishId: string) => {
  try {
    const response = await axiosInstance.delete(endpoints.dish.delete(dishId));
    return response.data;
  } catch (error) {
    console.error("Error deleting dish from host:", error);
    throw error;
  }
};

// get host dishes
export const getHostDishes = async (hostId: string) => {
  try {
    const response = await axiosInstance.get(endpoints.dish.all);
    return response.data;
  } catch (error) {
    console.error("Error getting host dishes:", error);
    throw error;
  }
};

// get dishes within map bounds
export const getDishesByMapBounds = async (
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  },
  filters?: {
    dishName?: string;
    title?: string;
    offering?: string;
    cuisine?: string;
    address?: string;
    date?: string;
    startTime?: string;
    endTime?: string;
  }
) => {
  try {
    const params = new URLSearchParams();
    params.set("published", "true");
    params.set("premade", "false");
    params.set("north", bounds.north.toString());
    params.set("south", bounds.south.toString());
    params.set("east", bounds.east.toString());
    params.set("west", bounds.west.toString());

    // Add existing filters
    if (filters?.dishName) params.set("dishName", filters.dishName);
    if (filters?.title) params.set("title", filters.title);
    if (filters?.offering) params.set("offering", filters.offering);
    if (filters?.cuisine) params.set("cuisine", filters.cuisine);
    if (filters?.date) params.set("date", filters.date);
    if (filters?.startTime) params.set("startTime", filters.startTime);
    if (filters?.endTime) params.set("endTime", filters.endTime);

    const response = await axiosInstance.get(
      `${endpoints.dish.all}?${params.toString()}`
    );
    const data = response.data.data;
    // Filter out DineIn offering from dishes if dineInAvailable is false and if no offering remains remove the dish
    const filteredData = data.filter((dish: any) => {
      if (dish.isAvailableInFuture) {
        // Remove Delivery offering from dish if dishAvailable is false
        dish.offering = dish.offering.filter((offer: any) => {
          if (offer === "Delivery") {
            return dish.dishAvailable;
          }
          return true;
        });
        if (dish.offering.length === 0) return false;

        const offeringParam = params.get("offering");
        console.log("offeringParam", offeringParam);
        if (
          offeringParam?.split(",")?.includes("Delivery") &&
          !dish.offering.includes("Delivery")
        ) {
          return false;
        }
        return true;
      }
    });
    return filteredData;
  } catch (error) {
    console.error("Error getting dishes by map bounds:", error);
    throw error;
  }
};
