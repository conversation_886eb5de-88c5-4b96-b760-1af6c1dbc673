"use client";
import React, { useState, useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Toaster, toast } from "sonner";
import Image from "next/image";
import { useRouter } from "@/routes/hooks";
import { paths } from "@/routes/paths";
import { Address } from "@/types/address";
import { locationHost } from "@/api/host";
import { useAuthContext } from "@/context/hooks/use-auth-hook";
import Script from "next/script";

// Define Zod schema for validation
const locationSchema = z.object({
  country: z.string().min(1, { message: "Country is required" }),
  state: z.string().min(1, { message: "State is required" }),
  city: z.string().min(1, { message: "City is required" }),
  street: z.string().min(1, { message: "Street is required" }),
  postalCode: z
    .string()
    .min(5, { message: "Zip Code must be exactly 5 digits" })
    .max(5, { message: "Zip Code must be exactly 5 digits" })
    .regex(/^\d{5}$/, { message: "Zip Code must contain only digits" }),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
});

// Infer form type
type LocationFormInputs = z.infer<typeof locationSchema>;

// Google Maps window extensions
declare global {
  interface Window {
    google: {
      maps: {
        Map: new (element: HTMLElement, options: any) => google.maps.Map;
        Marker: new (options: any) => google.maps.Marker;
        LatLng: new (lat: number, lng: number) => google.maps.LatLng;
        MapTypeId: { ROADMAP: string };
        Geocoder: new () => google.maps.Geocoder;
        ControlPosition: { RIGHT_BOTTOM: number };
        event: {
          addListener: (
            instance: any,
            eventName: string,
            handler: Function
          ) => void;
        };
        marker?: {
          AdvancedMarkerElement: new (
            options: any
          ) => google.maps.marker.AdvancedMarkerElement;
        };
      };
    };
    initMap?: () => void;
  }
}

// Props for the LocationForm component
type LocationFormProps = {
  onAddressPopup?: (data: LocationFormInputs) => void;
  initialAddress?: Address;
  googleMapsApiKey: string;
};

export function LocationForm({
  onAddressPopup,
  initialAddress,
  googleMapsApiKey,
}: LocationFormProps) {
  const router = useRouter();
  const { host } = useAuthContext();
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<google.maps.Map | null>(null);
  const markerRef = useRef<
    google.maps.Marker | google.maps.marker.AdvancedMarkerElement | null
  >(null);
  const geocoderRef = useRef<google.maps.Geocoder | null>(null);

  const [mapsLoaded, setMapsLoaded] = useState(false);
  const [isGeocodingLoading, setIsGeocodingLoading] = useState(false);
  const [mapInitialized, setMapInitialized] = useState(false);

  // Position state (default to Denver or initial address)
  const [position, setPosition] = useState({
    lat: initialAddress?.latitude || 39.7392,
    lng: initialAddress?.longitude || -104.9903,
  });

  // Form setup with validation
  const {
    register,
    reset,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
  } = useForm<LocationFormInputs>({
    resolver: zodResolver(locationSchema),
    defaultValues: initialAddress as any,
  });

  // Helper function to safely extract lat/lng values
  const extractLatLng = (latLng: any): { lat: number; lng: number } => {
    if (!latLng) return { lat: 0, lng: 0 };

    // Handle cases where lat/lng might be methods or properties
    const lat = typeof latLng.lat === "function" ? latLng.lat() : latLng.lat;
    const lng = typeof latLng.lng === "function" ? latLng.lng() : latLng.lng;

    return { lat: Number(lat), lng: Number(lng) };
  };

  // Check if Google Maps API is already loaded (for client-side navigation)
  useEffect(() => {
    if (window.google && window.google.maps && !mapsLoaded && !mapInitialized) {
      console.log("Google Maps API already loaded, initializing...");
      geocoderRef.current = new window.google.maps.Geocoder();
      initializeMap();
      setMapsLoaded(true);
    }

    // Listen for custom Google Maps loaded event
    const handleGoogleMapsLoaded = () => {
      if (
        window.google &&
        window.google.maps &&
        !mapsLoaded &&
        !mapInitialized
      ) {
        console.log("Google Maps loaded via custom event, initializing...");
        geocoderRef.current = new window.google.maps.Geocoder();
        initializeMap();
        setMapsLoaded(true);
      }
    };

    window.addEventListener("googleMapsLoaded", handleGoogleMapsLoaded);

    return () => {
      window.removeEventListener("googleMapsLoaded", handleGoogleMapsLoaded);
    };
  }, [mapsLoaded, mapInitialized]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      // Clean up map instance and event listeners
      if (googleMapRef.current) {
        window.google?.maps?.event?.clearInstanceListeners?.(
          googleMapRef.current
        );
        googleMapRef.current = null;
      }
      if (markerRef.current) {
        if ("removeEventListener" in markerRef.current) {
          // For AdvancedMarkerElement, remove event listeners if any
        } else if (markerRef.current instanceof window.google?.maps?.Marker) {
          window.google?.maps?.event?.clearInstanceListeners?.(
            markerRef.current
          );
        }
        markerRef.current = null;
      }
      geocoderRef.current = null;
      setMapInitialized(false);
      setMapsLoaded(false);
    };
  }, []);

  // Handle Google Maps script load
  const handleMapsLoad = () => {
    console.log("Google Maps script loaded");

    // Initialize geocoder
    if (window.google && window.google.maps && !mapInitialized) {
      geocoderRef.current = new window.google.maps.Geocoder();
      initializeMap();
      setMapsLoaded(true);
    }
  };

  // Initialize map after Google Maps script is loaded
  const initializeMap = () => {
    if (
      !mapRef.current ||
      !window.google ||
      !window.google.maps ||
      mapInitialized
    ) {
      return;
    }

    try {
      // Create the map
      const map = new window.google.maps.Map(mapRef.current, {
        center: { lat: position.lat, lng: position.lng },
        zoom: 13,
        mapTypeId: window.google.maps.MapTypeId.ROADMAP,
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: false,
        mapId: "4e0b276a99482460",
      });
      googleMapRef.current = map;

      // Create the marker (use AdvancedMarkerElement if available, fallback to regular Marker)
      if (
        window.google.maps.marker &&
        window.google.maps.marker.AdvancedMarkerElement
      ) {
        // Create marker element for visual
        const pinElement = document.createElement("div");
        pinElement.innerHTML = `
          <div style="cursor: pointer; width: 25px; height: 41px;">
            <svg viewBox="0 0 24 36" width="25" height="41">
              <path fill="#4285F4" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/>
              <circle fill="#FFFFFF" cx="12" cy="9" r="2.5"/>
            </svg>
          </div>
        `;

        // Create advanced marker
        const marker = new window.google.maps.marker.AdvancedMarkerElement({
          position: { lat: position.lat, lng: position.lng },
          map: map,
          content: pinElement,
          gmpDraggable: true,
          title: "Drag to set location",
        });
        markerRef.current = marker;

        // Add drag event handlers
        marker.addEventListener("dragend", () => {
          if (marker.position) {
            const newPosition = extractLatLng(marker.position);
            setPosition(newPosition);
            fetchAddressFromCoordinates(newPosition.lat, newPosition.lng);
          }
        });
      } else {
        // Fallback to regular Marker
        const marker = new window.google.maps.Marker({
          position: { lat: position.lat, lng: position.lng },
          map: map,
          draggable: true,
        });
        markerRef.current = marker;

        // Add drag event handlers
        window.google.maps.event.addListener(marker, "dragend", () => {
          const markerPosition = marker.getPosition();
          if (markerPosition) {
            const newPosition = {
              lat: markerPosition.lat(),
              lng: markerPosition.lng(),
            };
            setPosition(newPosition);
            fetchAddressFromCoordinates(newPosition.lat, newPosition.lng);
          }
        });
      }

      // Add click handler to map
      window.google.maps.event.addListener(
        map,
        "click",
        (event: google.maps.MapMouseEvent) => {
          if (event.latLng) {
            const clickPos = {
              lat: event.latLng.lat(),
              lng: event.latLng.lng(),
            };

            // Update position state
            setPosition(clickPos);

            // Update marker position
            if (markerRef.current) {
              if ("position" in markerRef.current) {
                // For AdvancedMarkerElement
                markerRef.current.position = clickPos;
              } else if (
                markerRef.current instanceof window.google.maps.Marker
              ) {
                // For regular Marker
                markerRef.current.setPosition(clickPos);
              }
            }

            // Get address for this location
            fetchAddressFromCoordinates(clickPos.lat, clickPos.lng);
          }
        }
      );

      // Mark map as initialized
      setMapInitialized(true);
      console.log("Map initialized successfully");
    } catch (error) {
      console.error("Error initializing map:", error);
      toast.error("Could not initialize the map. Please try again.");
      setMapInitialized(false);
    }
  };

  // Update map and marker when position changes
  useEffect(() => {
    if (
      mapsLoaded &&
      mapInitialized &&
      googleMapRef.current &&
      markerRef.current
    ) {
      // Center map on new position
      googleMapRef.current.panTo({ lat: position.lat, lng: position.lng });

      // Update marker position
      if ("position" in markerRef.current) {
        // For AdvancedMarkerElement
        markerRef.current.position = { lat: position.lat, lng: position.lng };
      } else if (markerRef.current instanceof google.maps.Marker) {
        // For regular Marker
        markerRef.current.setPosition({ lat: position.lat, lng: position.lng });
      }
    }
  }, [position, mapsLoaded, mapInitialized]);

  // Get current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      toast.loading("Getting your location...");

      navigator.geolocation.getCurrentPosition(
        (pos) => {
          toast.dismiss();
          const { latitude, longitude } = pos.coords;

          setPosition({
            lat: latitude,
            lng: longitude,
          });

          // Get address for this location
          if (geocoderRef.current) {
            fetchAddressFromCoordinates(latitude, longitude);
            toast.success("Location found!");
          }
        },
        (error) => {
          toast.dismiss();
          console.error("Geolocation error:", error);
          toast.error(
            "Could not get your location. Please check your permissions."
          );
        },
        { enableHighAccuracy: true, timeout: 10000 }
      );
    } else {
      toast.error("Geolocation is not supported by your browser.");
    }
  };

  // Fetch address from coordinates using Geocoding
  const fetchAddressFromCoordinates = async (lat: number, lng: number) => {
    if (!geocoderRef.current) return;

    try {
      setIsGeocodingLoading(true);

      const result = await geocoderRef.current.geocode({
        location: { lat, lng },
      });

      if (result.results && result.results.length > 0) {
        const addressComponents = result.results[0].address_components;
        console.log("Address Components:", addressComponents);

        // Extract address components
        const streetNumber =
          addressComponents.find((comp) => comp.types.includes("street_number"))
            ?.long_name || "";

        const route =
          addressComponents.find((comp) => comp.types.includes("route"))
            ?.long_name || "";

        const city =
          addressComponents.find(
            (comp) =>
              comp.types.includes("locality") ||
              comp.types.includes("sublocality")
          )?.long_name || "";

        const state =
          addressComponents.find((comp) =>
            comp.types.includes("administrative_area_level_1")
          )?.long_name || "";

        const country =
          addressComponents.find((comp) => comp.types.includes("country"))
            ?.long_name || "";

        const postalCode =
          addressComponents.find((comp) => comp.types.includes("postal_code"))
            ?.long_name || "";

        // Update form values
        setValue("street", streetNumber ? `${streetNumber} ${route}` : route);
        setValue("city", city);
        setValue("state", state);
        setValue("country", country);

        if (postalCode) {
          // Ensure postal code is 5 digits for validation
          setValue("postalCode", postalCode.substring(0, 5));
        }
      }
    } catch (error) {
      console.error("Geocoding error:", error);
    } finally {
      setIsGeocodingLoading(false);
    }
  };

  // Find location on map from address fields
  const findOnMap = async () => {
    if (!geocoderRef.current) {
      toast.error("Map services not initialized yet");
      return;
    }

    try {
      setIsGeocodingLoading(true);

      const { street, city, state, country, postalCode } = getValues();
      const addressString = `${street}, ${city}, ${state} ${postalCode}, ${country}`;

      const result = await geocoderRef.current.geocode({
        address: addressString,
      });

      if (result.results && result.results.length > 0) {
        const location = result.results[0].geometry.location;
        const newPosition = extractLatLng(location);

        setPosition(newPosition);
        toast.success("Address found on map!");
      } else {
        toast.error("Could not find this address on the map");
      }
    } catch (error) {
      console.error("Geocoding error:", error);
      toast.error("Failed to find the address");
    } finally {
      setIsGeocodingLoading(false);
    }
  };

  // Form submission handler
  const onSubmit = async (data: LocationFormInputs) => {
    const locationData = {
      ...data,
      latitude: position.lat,
      longitude: position.lng,
    };

    try {
      if (onAddressPopup) {
        onAddressPopup(locationData);
        toast.success("Address updated successfully");
      } else {
        console.log(host);
        console.log(locationData);
        const hostLocation = await locationHost(host._id, locationData);
        toast.success(hostLocation?.message || "Location saved successfully");
        router.push(paths.host.diningLocation);
        reset();
      }
    } catch (error: any) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else if (
        typeof error === "object" &&
        error !== null &&
        "response" in error
      ) {
        const apiError = error as { response: { data: { message: string } } };
        toast.error(apiError.response.data.message);
      } else {
        toast.error("Something went wrong");
      }
    }
  };

  return (
    <div className="w-full max-w-md bg-white flex flex-col gap-1">
      <Script
        src={`https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=places,marker&callback=initMapCallback`}
        onLoad={handleMapsLoad}
        strategy="afterInteractive"
      />

      <Script id="google-maps-init" strategy="afterInteractive">
        {`
          window.initMapCallback = function() {
            console.log("Google Maps initialized via callback");
            // Trigger the React component's initialization if it hasn't happened yet
            if (window.google && window.google.maps) {
              window.dispatchEvent(new CustomEvent('googleMapsLoaded'));
            }
          }
        `}
      </Script>

      <Toaster richColors position="bottom-left" />

      <div className="flex items-center mx-4">
        <Image src="/applogo.png" alt="Logo" width={40} height={40} />
        <Image
          src="/eatsexpress.png"
          alt="App Name"
          width={140}
          height={20}
          className="ml-2"
        />
      </div>

      <h1 className="w-full text-m font-semibold text-start px-4 mt-[0.5rem]">
        Where are you hosting?
      </h1>

      <form
        onSubmit={handleSubmit(onSubmit)}
        className="px-4 flex flex-col gap-1"
      >
        {/* Map Container */}
        <div className="relative rounded-lg overflow-hidden">
          <div
            ref={mapRef}
            className="h-48 w-full rounded-lg"
            style={{ background: "#f0f0f0" }}
          ></div>

          {/* Current Location Button */}
          <button
            type="button"
            onClick={getCurrentLocation}
            className="absolute bottom-2 right-2 bg-white p-2 rounded-full shadow-md hover:bg-gray-100"
            title="Use my current location"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="3"></circle>
            </svg>
          </button>

          {/* Loading Overlay */}
          {(!mapsLoaded || !mapInitialized) && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-200 bg-opacity-50">
              <div className="flex flex-col items-center gap-2">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                <p className="text-sm text-gray-600">
                  {!mapsLoaded ? "Loading map..." : "Initializing map..."}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Form Fields */}
        <div className="flex flex-col gap-2">
          <div>
            <label className="text-sm">Country</label>
            <input
              placeholder="United States"
              type="text"
              {...register("country")}
              className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 ${
                errors.country ? "border-red-500" : "border-gray-300"
              }`}
            />
            {errors.country && (
              <p className="text-red-600 text-sm">{errors.country.message}</p>
            )}
          </div>

          <div>
            <label className="text-sm">State</label>
            <input
              type="text"
              {...register("state")}
              placeholder="Colorado"
              className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 ${
                errors.state ? "border-red-500" : "border-gray-300"
              }`}
            />
            {errors.state && (
              <p className="text-red-600 text-sm">{errors.state.message}</p>
            )}
          </div>

          <div>
            <label className="text-sm">City</label>
            <input
              type="text"
              {...register("city")}
              placeholder="Denver"
              className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 ${
                errors.city ? "border-red-500" : "border-gray-300"
              }`}
            />
            {errors.city && (
              <p className="text-red-600 text-sm">{errors.city.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm">Street</label>
              <input
                type="text"
                {...register("street")}
                placeholder="Cherry Creek"
                className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 ${
                  errors.street ? "border-red-500" : "border-gray-300"
                }`}
              />
              {errors.street && (
                <p className="text-red-600 text-sm">{errors.street.message}</p>
              )}
            </div>

            <div>
              <label className="text-sm">Zip Code</label>
              <input
                type="text"
                {...register("postalCode")}
                placeholder="80201"
                className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 ${
                  errors.postalCode ? "border-red-500" : "border-gray-300"
                }`}
              />
              {errors.postalCode && (
                <p className="text-red-600 text-sm">
                  {errors.postalCode.message}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Find on Map Button */}
        <button
          type="button"
          onClick={findOnMap}
          disabled={isGeocodingLoading}
          className="w-full py-2 mt-3 bg-blue-500 text-white font-semibold rounded-md hover:bg-blue-600 transition-colors disabled:bg-blue-300"
        >
          {isGeocodingLoading ? "Finding location..." : "Find on Map"}
        </button>

        {/* Submit Button */}
        <button
          type="submit"
          className="w-full py-2 mt-2 bg-green-500 text-white font-semibold rounded-md hover:bg-green-600 transition-colors shadow-md"
        >
          {onAddressPopup ? "Update Address" : "Next"}
        </button>
      </form>
    </div>
  );
}
