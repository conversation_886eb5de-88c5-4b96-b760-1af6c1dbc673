import { Address } from "./address";
export interface Dish {
  isAvailableInFuture: any;
  _id: string;
  name: string;
  description: string;
  cuisine: Cuisine;
  price: number;
  photos: string[];
  offering: string[];
  ingredients: string[];
  diningLocation: string;
  averageRating?: number;
  minServings: number;
  maxServings: number;
  premade: boolean;
  published?: boolean;
  dishAvailable?: boolean;
  subcuisine?: string;
  discount?: number;
  currency: string;
  availability: {
    date: Date;
    startTime: string;
    endTime: string;
  }[];
}

export interface Ingredient {
  name: string;
}

export interface Rating {
  user: string;
}

export interface DiningLocations {
  _id: string;
  locationName: string;
  images: string[];
}

export interface Cuisine {
  _id: string;
  name: string;
  code: string;
}

export interface HostCardProps {
  images: string[];
  title: string;
  locationName: string;
  rating: number;
  priceRange: string;
  diningIcons?: string[];
  cuisines: Cuisine[];
  dishes: Dish[];
  address: Address;
  onClick?: () => void;
  isHearted?: boolean;
}

export interface Host {
  _id: string;
  title: string;
  images: string[];
  diningLocations: any[];
  address: Address;
  dishes: Dish[];
  cuisine: Cuisine[];
  priceMin: number;
  priceMax: number;
  averageRating: number;
  offering: "Dine In" | "Takeaway" | "Delivery"[];
  openHours: Record<string, any>;
}
