import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { generateTimeOptions, isValidTimeRange } from "@/utils/dateTimeUtils";
import { cn } from "@/lib/utils";

interface TimePickerProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

interface TimeRangePickerProps {
  startTime: string;
  endTime: string;
  onStartTimeChange: (value: string) => void;
  onEndTimeChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
}

const timeOptions = generateTimeOptions();

export function TimePicker({
  value,
  onChange,
  placeholder = "Select time",
  disabled = false,
  className,
}: TimePickerProps) {
  return (
    <Select value={value} onValueChange={onChange} disabled={disabled}>
      <SelectTrigger className={cn("w-[120px]", className)}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {timeOptions.map((time) => (
          <SelectItem key={time} value={time}>
            {time}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export function TimeRangePicker({
  startTime,
  endTime,
  onStartTimeChange,
  onEndTimeChange,
  disabled = false,
  className,
}: TimeRangePickerProps) {
  const isValidRange =
    startTime && endTime ? isValidTimeRange(startTime, endTime) : true;

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="flex flex-col gap-1">
        <label className="text-xs text-gray-600">Start Time</label>
        <TimePicker
          value={startTime}
          onChange={onStartTimeChange}
          placeholder="Start"
          disabled={disabled}
          className="w-[100px]"
        />
      </div>

      <div className="flex items-center justify-center pt-4">
        <span className="text-gray-400">—</span>
      </div>

      <div className="flex flex-col gap-1">
        <label className="text-xs text-gray-600">End Time</label>
        <TimePicker
          value={endTime}
          onChange={onEndTimeChange}
          placeholder="End"
          disabled={disabled}
          className="w-[100px]"
        />
      </div>

      {startTime && endTime && !isValidRange && (
        <div className="text-xs text-red-500 mt-1">
          End time must be after start time
        </div>
      )}
    </div>
  );
}
