import type React from "react";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreVertical, TriangleAlert } from "lucide-react";
import { Button } from "@/components/ui/button";
import type { Dish } from "@/types/host";
import Carousel from "@/components/ui/Carousal";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface DishCardProps {
  dish: Dish;
  onEdit?: () => void;
  onDelete?: () => void;
  toggleDishPublish?: () => void;
  hideActions?: boolean;
}

const DishCard: React.FC<DishCardProps> = ({
  dish,
  onEdit,
  onDelete,
  toggleDishPublish,
  hideActions,
}) => (
  <Card className="w-80 border transition-shadow duration-300">
    <CardContent className="p-0">
      <Carousel images={dish.photos} alt={dish.name} />
      <div className="p-4">
        <h3 className="font-semibold text-lg mb-2">{dish.name}</h3>
        <p className="text-xl font-semibold">
          {dish.currency} {dish.price.toFixed(2)}
          {dish.discount && dish.discount > 0 && (
            <span className="ml-2 text-sm text-green-600">
              ({dish.discount}% off)
            </span>
          )}
        </p>
        <p className="text-sm text-gray-600 mt-2 line-clamp-2">
          {dish.description}
        </p>
        <p className="text-sm mt-2">
          Servings: {dish.minServings} - {dish.maxServings}
        </p>
      </div>
    </CardContent>
    <CardFooter className="flex justify-between items-center p-4">
      <div className="flex items-center space-x-2">
        <span
          className={`text-sm ${
            dish.published
              ? dish.isAvailableInFuture
                ? "text-green-600"
                : "text-yellow-600"
              : "text-red-600"
          }`}
        >
          {dish.published ? "Published" : "Unpublished"}
        </span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger className="flex items-center">
              {dish.published && !dish.isAvailableInFuture && (
                <TriangleAlert className="h-4 w-4 text-yellow-600 mr-1" />
              )}
            </TooltipTrigger>
            {dish.published && !dish.isAvailableInFuture && (
              <TooltipContent side="top">
                <p className="text-sm">
                  Published but unavailable : Change Availability
                </p>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      </div>
      {!hideActions && onEdit && onDelete && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {toggleDishPublish && (
              <DropdownMenuItem onClick={toggleDishPublish}>
                {dish.published ? "Unpublish" : "Publish"}
              </DropdownMenuItem>
            )}
            {onEdit && (
              <DropdownMenuItem onClick={onEdit}>Edit</DropdownMenuItem>
            )}
            {onDelete && (
              <DropdownMenuItem onClick={onDelete} className="text-red-600">
                Delete
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </CardFooter>
  </Card>
);

export default DishCard;
