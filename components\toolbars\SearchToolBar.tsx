// src/components/toolbars/SearchToolBar.tsx
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, X, ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import PlaceFilter from "./PlaceFilter"; // Import the new component
import CuisineDropdown from "@/components/ui/CuisineDropdown";
import { DateTimeFilter } from "@/components/ui/date-time-filter";

interface CuisineOption {
  label: string;
  value: string;
}

interface SearchToolbarProps {
  // Renamed props for clarity
  address: string; // Changed from 'search'
  onAddressChange: (
    address: string,
    lat?: number,
    lng?: number,
    bounds?: google.maps.LatLngBounds
  ) => void; // Changed from 'onSearchDestinations'
  isMapApiLoaded: boolean; // New prop
  googleMapsApiKey: string; // New prop
  libraries: ("places" | "drawing" | "geometry" | "visualization")[]; // New prop

  dishName: string;
  onDishSelect: (value: string) => void;

  // Date and time filtering props
  selectedDate?: Date;
  startTime: string;
  endTime: string;
  onDateChange: (date: Date | undefined) => void;
  onStartTimeChange: (time: string) => void;
  onEndTimeChange: (time: string) => void;
  onDateTimeClear: () => void;

  offering: string[];
  onDineInSelect: (values: string[]) => void;

  // New props for cuisine dropdown and clear filters
  cuisines?: CuisineOption[];
  selectedCuisine?: string;
  onCuisineSelect?: (value: string) => void;
  onClearFilters?: () => void;
  showSplitView?: boolean; // To determine when to show cuisine dropdown

  onSearchClick: () => void; // Keep the main search trigger
  isLoading?: boolean; // Add loading state
}

const HomeSearchToolbar: React.FC<SearchToolbarProps> = ({
  address,
  onAddressChange,
  isMapApiLoaded,
  googleMapsApiKey,
  libraries,
  dishName,
  selectedDate,
  startTime,
  endTime,
  onDateChange,
  onStartTimeChange,
  onEndTimeChange,
  onDateTimeClear,
  offering,
  onSearchClick,
  onDishSelect,
  onDineInSelect,
  cuisines = [],
  selectedCuisine = "",
  onCuisineSelect,
  onClearFilters,
  showSplitView = false,
  isLoading = false,
}) => {
  // Handle custom search trigger for manual text input
  const handleSearchTrigger = (query: string) => {
    // This triggers when user searches with custom text that wasn't autocompleted
    console.log("Custom search triggered for:", query);
    onSearchClick(); // Trigger the main search function
  };
  return (
    <div className="text-wrap flex flex-col md:flex-row items-center justify-between gap-1 md:gap-2 bg-background p-2 rounded-full border border-border shadow-md w-full max-w-4xl mx-auto">
      {/* Place Filter replaces the old address input */}
      <div className="flex-grow w-full md:w-auto">
        <PlaceFilter
          initialValue={address}
          onPlaceSelect={onAddressChange}
          onSearchTrigger={handleSearchTrigger}
          isMapApiLoaded={isMapApiLoaded}
          googleMapsApiKey={googleMapsApiKey}
          libraries={libraries}
          placeholder="Where"
          allowCustomInput={true}
          disabled={isLoading}
        />
      </div>
      {/* Divider */}
      <div className="hidden md:block h-5 border-l border-gray-300"></div>

      {/* Other Filters (Dish Name, Date/Title, Offering) */}
      <div className="flex-grow w-full md:w-auto">
        <Input
          type="text"
          placeholder="Dish name..."
          value={dishName}
          onChange={(e) => onDishSelect(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && onSearchClick()}
          className="border-none focus-visible:ring-0 shadow-none text-sm h-8"
          disabled={isLoading}
          aria-label="Search by dish name"
          autoComplete="off"
        />
      </div>
      <div className="hidden md:block h-5 border-l border-gray-300"></div>

      {/* Date and Time Filter */}
      <div className="flex-grow w-full md:w-auto">
        <DateTimeFilter
          selectedDate={selectedDate}
          startTime={startTime}
          endTime={endTime}
          onDateChange={onDateChange}
          onStartTimeChange={onStartTimeChange}
          onEndTimeChange={onEndTimeChange}
          onClear={onDateTimeClear}
          disabled={isLoading}
          placeholder="When?"
          className="w-full"
        />
      </div>
      <div className="hidden md:block h-5 border-l border-gray-300"></div>

      {/* Offering Filter Dropdown - Multiple Selection */}
      <div className="flex-grow w-full md:w-auto">
        <DropdownMenu>
          <TooltipInputWrapper
            tooltipContent={offeringDisplayText}
            showTooltip={isOfferingOverflowing}
            side="top"
          >
            <DropdownMenuTrigger asChild>
              <Button
                ref={offeringRef as React.RefObject<HTMLButtonElement>}
                variant="ghost"
                className="h-8 px-3 text-sm border-none focus-visible:ring-0 shadow-none justify-between min-w-[120px] max-w-[150px]"
                disabled={isLoading}
              >
                <span className="truncate">
                  {offering.length === 0
                    ? "Offering"
                    : offering.length === 1
                    ? offering[0]
                    : offering.length <= 2
                    ? offering.join(", ")
                    : `${offering.length} selected`}
                </span>
                <ChevronDown className="h-3 w-3 ml-1 flex-shrink-0" />
              </Button>
            </DropdownMenuTrigger>
          </TooltipInputWrapper>
          <DropdownMenuContent align="start" className="w-48">
            <DropdownMenuItem
              onClick={() => onDineInSelect([])}
              className="cursor-pointer"
            >
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border border-gray-300 rounded flex items-center justify-center">
                  {offering.length === 0 && (
                    <div className="w-2 h-2 bg-primary rounded"></div>
                  )}
                </div>
                <span>Clear All</span>
              </div>
            </DropdownMenuItem>
            <div className="border-t my-1"></div>
            {["Dine In", "Delivery", "Take Away"].map((option) => (
              <DropdownMenuItem
                key={option}
                onClick={() => {
                  const isSelected = offering.includes(option);
                  if (isSelected) {
                    onDineInSelect(offering.filter((item) => item !== option));
                  } else {
                    onDineInSelect([...offering, option]);
                  }
                }}
                className="cursor-pointer"
              >
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border border-gray-300 rounded flex items-center justify-center">
                    {offering.includes(option) && (
                      <div className="w-2 h-2 bg-primary rounded"></div>
                    )}
                  </div>
                  <span>{option}</span>
                </div>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Cuisine Dropdown - Only show in split view */}
      {showSplitView && cuisines.length > 0 && onCuisineSelect && (
        <>
          <div className="hidden md:block h-5 border-l border-gray-300"></div>
          <div className="flex-grow w-full md:w-auto">
            <CuisineDropdown
              cuisines={cuisines}
              selectedCuisine={selectedCuisine}
              onCuisineSelect={onCuisineSelect}
              disabled={isLoading}
              placeholder="Cuisine"
            />
          </div>
        </>
      )}

      {/* Clear Filters Button */}
      {onClearFilters && (
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full w-8 h-8 flex-shrink-0 text-gray-500 hover:text-gray-700"
          onClick={onClearFilters}
          aria-label="Clear all filters"
          disabled={isLoading}
          title="Clear all filters"
        >
          <X className="h-4 w-4" />
        </Button>
      )}

      {/* Search Button */}
      <Button
        size="icon"
        className="rounded-full bg-primary text-primary-foreground w-8 h-8 flex-shrink-0"
        onClick={onSearchClick}
        aria-label="Search"
        disabled={isLoading}
      >
        {isLoading ? (
          <div className="h-4 w-4 border-2 border-t-transparent border-white rounded-full animate-spin" />
        ) : (
          <Search className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
};

export default HomeSearchToolbar;
