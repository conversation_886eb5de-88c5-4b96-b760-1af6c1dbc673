import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ChefHat, Plus, Search, Utensils } from "lucide-react";
import { useRouter } from "next/navigation";

interface EmptyStateProps {
  title?: string;
  description?: string;
  showCreateRequest?: boolean;
  className?: string;
  variant?: "default" | "compact";
}

export function EmptyState({
  title = "No dishes found",
  description = "No dishes found matching your criteria. Try adjusting your search or filters.",
  showCreateRequest = true,
  className = "",
  variant = "default",
}: EmptyStateProps) {
  const router = useRouter();

  const handleCreateRequest = () => {
    router.push("/requests/request-form");
  };

  if (variant === "compact") {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
            <p className="text-sm text-gray-500 max-w-md">{description}</p>
          </div>
          {showCreateRequest && (
            <Button
              onClick={handleCreateRequest}
              className="mt-4 bg-green-600 hover:bg-green-700 text-white"
              size="sm"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create a Request
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full flex justify-center items-center py-12 ${className}`}>
      <Card className="max-w-md w-full mx-4 border-dashed border-2 border-gray-200 bg-gray-50/50">
        <CardContent className="pt-8 pb-8">
          <div className="text-center space-y-6">
            {/* Icon Section */}
            <div className="flex justify-center space-x-2">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                <ChefHat className="w-6 h-6 text-orange-600" />
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <Utensils className="w-6 h-6 text-green-600" />
              </div>
            </div>

            {/* Text Content */}
            <div className="space-y-3">
              <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
              <p className="text-gray-600 text-sm leading-relaxed">{description}</p>
            </div>

            {/* Action Section */}
            {showCreateRequest && (
              <div className="space-y-3">
                <div className="w-full h-px bg-gray-200"></div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-700">
                    Can't find what you're looking for?
                  </p>
                  <Button
                    onClick={handleCreateRequest}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3"
                    size="lg"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Create a Request
                  </Button>
                  <p className="text-xs text-gray-500 mt-2">
                    Tell local hosts what you'd like to eat and they'll create it for you!
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
