import React, { useEffect } from "react";
import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { Calendar } from "@/components/ui/calendar";
import { TimePicker } from "@/components/ui/time-picker";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  addDays,
  format,
  formatISO,
  isAfter,
  isSameDay,
  parseISO,
  set,
  startOfToday,
} from "date-fns";
import type { DateRange } from "react-day-picker";

interface AvailabilityTime {
  date: Date;
  startTime: string;
  endTime: string;
  startTimeGMT?: string; // Add GMT fields
  endTimeGMT?: string; // Add GMT fields
}

const AvailabilityStep: React.FC = () => {
  const { control, watch, setValue } = useFormContext();
  const availability = watch("availability") || [];
  const dateRange = watch("dateRange") || {};
  const today = startOfToday();

  // Convert local time to GMT
  const convertToGMT = (date: Date, timeString: string): string => {
    const [hours, minutes] = timeString.split(":").map(Number);

    // Create a new date with the given time in local timezone
    const localDateTime = set(new Date(date), {
      hours,
      minutes,
      seconds: 0,
      milliseconds: 0,
    });

    // Convert to GMT and format
    return formatISO(localDateTime);
  };

  useEffect(() => {
    if (dateRange.from && dateRange.to) {
      const start = dateRange.from;
      const end = dateRange.to;
      const newAvailabilityTimes: AvailabilityTime[] = [];
      for (let date = start; date <= end; date = addDays(date, 1)) {
        const existingTime = availability.find((a: AvailabilityTime) =>
          isSameDay(parseISO(a.date.toString()), date)
        );
        if (existingTime) {
          newAvailabilityTimes.push(existingTime);
        } else {
          const startTime = "09:00";
          const endTime = "17:00";
          newAvailabilityTimes.push({
            date: date,
            startTime: startTime,
            endTime: endTime,
            startTimeGMT: convertToGMT(date, startTime), // Calculate and add GMT
            endTimeGMT: convertToGMT(date, endTime), // Calculate and add GMT
          });
        }
      }
      setValue("availability", newAvailabilityTimes);
    }
  }, [dateRange, setValue]);

  const handleTimeChange = (
    index: number,
    field: "startTime" | "endTime",
    value: string
  ) => {
    const newAvailability = [...availability];
    newAvailability[index][field] = value;

    // Recalculate GMT time when local time changes
    if (field === "startTime") {
      newAvailability[index].startTimeGMT = convertToGMT(
        newAvailability[index].date,
        value
      );
    } else if (field === "endTime") {
      newAvailability[index].endTimeGMT = convertToGMT(
        newAvailability[index].date,
        value
      );
    }

    setValue("availability", newAvailability);
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Set Dish Availability</h2>
      <div className="flex-row space-x-4 gap-16 items-center">
        <div className="w-full mb-2">
          <Controller
            name="dateRange"
            control={control}
            render={({ field }) => (
              <Calendar
                mode="range"
                selected={field.value}
                onSelect={(range: DateRange | undefined) =>
                  field.onChange(range)
                }
                numberOfMonths={2}
                className="rounded-md border"
                disabled={(date) => isAfter(today, date)} // Disable past dates
                fromDate={today} // Can only select dates from today
              />
            )}
          />
        </div>
        {availability.map((slot: AvailabilityTime, index: number) => (
          <div key={index} className="mb-4 p-4 border rounded-md">
            <p className="font-semibold mb-2">
              {format(new Date(slot.date), "MMMM d, yyyy")}
            </p>
            <div className="flex items-center space-x-2 mb-2">
              <TimePicker
                value={slot.startTime}
                onChange={(value) =>
                  handleTimeChange(index, "startTime", value)
                }
              />
              <span>to</span>
              <TimePicker
                value={slot.endTime}
                onChange={(value) => handleTimeChange(index, "endTime", value)}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AvailabilityStep;
