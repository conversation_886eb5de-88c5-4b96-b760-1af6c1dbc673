import Link from "next/link";
import React from "react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-white w-full h-[541px] pb-4 border-t-1 border flex flex-col justify-between pt-8 px-8 md:px-20 md:pt-20">
      <div className="flex flex-wrap justify-between items-start gap-8">
        {/* Left Section: Logo & Description */}
        <div className="w-full md:w-1/4 flex flex-col gap-4">
          <Link href="/">
            <img
              src="/famfoody-logo.png"
              alt="EatsExpress Logo"
              className=" h-12"
            />
          </Link>
          <p className="text-gray-600">
            Lorem ipsum dolor sit amet consectetur adipiscing elit aliquam.
          </p>
          <div className="flex gap-4 mt-4 items-center justify-start">
            <a href="#">
              <img src="/social/facebook.png" alt="Facebook" />
            </a>
            <a href="#">
              <img src="/social/twitter.png" alt="Twitter" />
            </a>
            <a href="#">
              <img src="/social/instagram.png" alt="Instagram" />
            </a>
            <a href="#">
              <img src="/social/linkedin.png" alt="LinkedIn" />
            </a>
            <a href="#">
              <img src="/social/youtube.png" alt="YouTube" />
            </a>
          </div>
        </div>

        {/* Product Links */}
        <div className="w-full md:w-1/5">
          <h4 className="font-semibold mb-2">Product</h4>
          <ul className="space-y-2">
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Features
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Pricing
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Case studies
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Reviews
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Updates
              </a>
            </li>
          </ul>
        </div>

        {/* Company Links */}
        <div className="w-full md:w-1/5">
          <h4 className="font-semibold mb-2">Company</h4>
          <ul className="space-y-2">
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                About
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Contact us
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Careers
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Culture
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Blog
              </a>
            </li>
          </ul>
        </div>

        {/* Support Links */}
        <div className="w-full md:w-1/5">
          <h4 className="font-semibold mb-2">Support</h4>
          <ul className="space-y-2">
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Getting started
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Help center
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Server status
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Report a bug
              </a>
            </li>
            <li>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Chat support
              </a>
            </li>
          </ul>
        </div>

        {/* Contacts */}
        <div className="w-full md:w-1/5">
          <h4 className="font-semibold mb-2">Contacts us</h4>
          <ul className="space-y-2">
            <li className="text-gray-600">
              <a
                href="mailto:<EMAIL>"
                className="hover:text-gray-900"
              >
                <EMAIL>
              </a>
            </li>
            <li className="text-gray-600">
              <a href="tel:+14146875892" className="hover:text-gray-900">
                (414) 687 - 5892
              </a>
            </li>
            <li className="text-gray-600">
              794 Mcallister St, San Francisco, 94102
            </li>
          </ul>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="w-full flex justify-center items-center mt-8 text-sm text-gray-600 border-t pt-4">
        <p>All Rights Reserved</p>
        <div className="flex gap-1 ml-1">
          <div className="border border-[#EBEBEB] rotate-[-180deg]" />
          <a href="#" className="hover:text-green-500 text-green-500 underline">
            Terms and Conditions
          </a>
          <div className="border border-[#EBEBEB] rotate-[-180deg]" />
          <a href="#" className="hover:text-green-500 text-green-500 underline">
            Privacy Policy
          </a>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
