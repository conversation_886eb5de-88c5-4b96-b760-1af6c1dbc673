"use client";

import type React from "react";
import { useState, useCallback } from "react";
import { useForm, FormProvider } from "react-hook-form";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import StepIndicator from "./StepIndicator";
import DishTypeStep from "./steps/DishTypeStep";
import CuisineStep from "./steps/CuisineStep";
import DishDetailsStep from "./steps/DishDetailsStep";
import ImageUploadStep from "./steps/ImageUploadStep";
import IngredientsStep from "./steps/IngredientsStep";
import ServingsStep from "./steps/ServingsStep";
import OfferingStep from "./steps/OfferingStep";
import PricingStep from "./steps/PricingStep";
import ReviewStep from "./steps/ReviewStep";
import type { DiningLocations, Dish } from "@/types/host";
import DiningLocationStep from "./steps/DiningLocatinStep";
import AvailabilityStep from "./steps/AvailabilityStep";
import { createDishHost } from "@/api/host";

type DishFormData = {
  _id: string;
  name: string;
  price: number;
  discount?: number;
  description: string;
  diningLocation: string;
  offering: string[];
  photos: string[];
  ingredients: string[];
  cuisine: string;
  subcuisine?: string;
  minServings: number;
  maxServings: number;
  premade: boolean;
  currency: string;
  availability: {
    date: Date;
    startTime: string;
    endTime: string;
    startTimeGMT?: string; // Add GMT fields
    endTimeGMT?: string; // Add GMT fields
  }[];
};

type FullPageAddDishProps = {
  onAddDish: (dish: DishFormData) => void;
  onClose: () => void;
  ingredients: { value: string; label: string }[];
  cuisines: { value: string; label: string; subcuisines: string[] }[];
  diningLocations: DiningLocations[];
  premadeDishes: any[];
  initialData?: Dish;
  isEditing?: boolean;
};

const steps = [
  "Dish Type",
  "Cuisine",
  "Details",
  "Image",
  "Ingredients",
  "Servings",
  "Offering",
  "Pricing",
  "Dining Location",
  "Availability",
  "Review",
];

const FullPageAddDish: React.FC<FullPageAddDishProps> = ({
  onAddDish,
  onClose,
  ingredients,
  cuisines,
  premadeDishes,
  diningLocations,
  initialData,
  isEditing = false,
}) => {
  console.log("initialData", initialData);
  console.log("isEditing", isEditing);
  const [currentStep, setCurrentStep] = useState(isEditing ? 10 : 0);
  const methods = useForm<DishFormData>({
    mode: "onChange",
    defaultValues: initialData
      ? {
          ...initialData,
          cuisine: initialData.cuisine._id,
        }
      : {},
  });

  const {
    formState: { isValid, errors },
  } = methods;

  const fieldsByStep: Record<number, (keyof DishFormData)[]> = {
    0: ["cuisine", "subcuisine"],
    1: [],
    2: ["name", "description"],
    3: ["photos"],
    4: ["ingredients"],
    5: ["minServings", "maxServings"],
    6: ["offering"],
    7: ["price", "discount", "currency"],
    8: ["diningLocation"],
    9: ["availability"],
    10: [],
  };

  const nextStep = useCallback(() => {
    if (currentStep < steps.length - 1) {
      methods.trigger(fieldsByStep[currentStep]).then((isStepValid) => {
        if (isStepValid) {
          setCurrentStep((prev) => prev + 1);
        }
      });
    }
  }, [currentStep, methods, fieldsByStep]);

  const prevStep = () => setCurrentStep((prev) => Math.max(prev - 1, 0));

  methods.watch((data) => console.log(data));

  const onSubmit = (data: DishFormData) => {
    onAddDish(data);
    onClose();
  };

  const renderStep = useCallback(() => {
    switch (currentStep) {
      case 0:
        return <CuisineStep cuisines={cuisines} />;
      case 1:
        return <DishTypeStep premadeDishes={premadeDishes} />;
      case 2:
        return <DishDetailsStep />;
      case 3:
        return <ImageUploadStep />;
      case 4:
        return <IngredientsStep ingredients={ingredients} />;
      case 5:
        return <ServingsStep />;
      case 6:
        return <OfferingStep />;
      case 7:
        return <PricingStep />;
      case 8:
        return <DiningLocationStep dininglocations={diningLocations} />;
      case 9:
        return <AvailabilityStep />;
      case 10:
        return (
          <ReviewStep
            onEditStep={setCurrentStep}
            cuisines={cuisines}
            diningLocations={diningLocations}
          />
        );
      default:
        return null;
    }
  }, [currentStep, cuisines, ingredients, premadeDishes, diningLocations]);

  const isNextDisabled = useCallback(() => {
    const currentFields = fieldsByStep[currentStep] || [];
    return currentFields.some((field) => !!errors[field]);
  }, [currentStep, errors]);

  return (
    <div className="fixed inset-0 bg-white z-50 overflow-auto">
      <FormProvider {...methods}>
        <form
          onSubmit={(e) => {
            e.preventDefault(); // Prevent default form submission
            if (currentStep === steps.length - 1) {
              methods.handleSubmit(onSubmit)(); // Trigger manual submission if on the last step
            }
          }}
          className="h-full flex flex-col"
        >
          <header className="flex justify-between items-center py-6 px-12">
            <div className="flex items-center justify-center">
              <Link href="/">
                <img src="/applogo.png" alt="App Logo" className="h-8 w-8" />
              </Link>
            </div>
            <h1 className="text-2xl font-bold">Create Dish</h1>
            <Button variant="outline" onClick={onClose}>
              Save & Exit
            </Button>
          </header>

          <main className="flex-grow p-6 overflow-auto w-1/3 mx-auto">
            {renderStep()}
          </main>

          <footer className="border-t py-6 px-12">
            <StepIndicator steps={steps} currentStep={currentStep} />
            <div className="flex justify-between mt-4">
              <Button
                type="button"
                onClick={prevStep}
                disabled={currentStep === 0}
                variant="outline"
              >
                Back
              </Button>
              {currentStep === steps.length - 1 ? (
                <Button
                  type="button"
                  disabled={!isValid}
                  onClick={() => methods.handleSubmit(onSubmit)()}
                >
                  Finish
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={isNextDisabled()}
                >
                  Next
                </Button>
              )}
            </div>
          </footer>
        </form>
      </FormProvider>
    </div>
  );
};

export default FullPageAddDish;
